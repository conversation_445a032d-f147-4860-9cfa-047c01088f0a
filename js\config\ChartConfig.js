/**
 * 图表公共配置模块
 * 抽取各个图表组件的公共配置，便于统一管理和维护
 */

/**
 * API配置
 */
export const API_CONFIG = {
  // 基础API地址 - 从环境变量获取
  BASE_URL: import.meta.env.VITE_API_BASE_URL || "/dwyztApp/dwyzt",

  // 实时数据API配置
  REALTIME_DATA: {
    url: "/getDataByRtKeyId",
    // 数据类型映射
    DATA_TYPES: {
      REAL_TIME_ACE: "130010:320000000000010034", // 实时ACE
      CURRENT_COST: "130037:320000000000010010", // 当班费用
    },
  },

  // 断面监视API配置
  SECTION_DATA: {
    url: "/getSectionTop5",
  },

  // CPS数据API配置
  CPS_DATA: {
    url: "/getCPSList",
  },
  REALTIME_CPS_DATA: {
    url: "/getDataByRtKeyId",
    DATA_TYPES: {
      CPS1: "130010:320000000000010076", // CPS1
      CPS2: "130010:320000000000010078", // CPS2
    },
  },
};

/**
 * 构建实时数据API URL
 * @param {Array} dataTypes 数据类型数组，如 ['REAL_TIME_ACE', 'CURRENT_COST']
 * @returns {string} 完整的API URL
 */
export function buildRealtimeDataUrl(
  dataTypes = ["REAL_TIME_ACE", "CURRENT_COST"]
) {
  const rtKeyStr = dataTypes
    .map((type) => API_CONFIG.REALTIME_DATA.DATA_TYPES[type])
    .filter(Boolean)
    .join(",");

  return `${API_CONFIG.BASE_URL}${API_CONFIG.REALTIME_DATA.url}?rtKeyStr=${rtKeyStr}`;
}

/**
 * 构建CPS数据API URL
 * @param {string} startDay 开始日期，格式：YYYY-MM-DD
 * @param {string} endDay 结束日期，格式：YYYY-MM-DD
 * @returns {string} 完整的API URL
 */
export function buildCPSDataUrl(startDay, endDay) {
  return `${API_CONFIG.BASE_URL}${API_CONFIG.CPS_DATA.url}?startDay=${startDay}&endDay=${endDay}`;
}

/**
 * 构建实时CPS数据API URL
 * @param {Array} dataTypes 数据类型数组，如 ['CPS1', 'CPS2']
 * @returns {string} 完整的API URL
 */
export function buildRealtimeCPSDataUrl(dataTypes = ["CPS1", "CPS2"]) {
  const rtKeyStr = dataTypes
    .map((type) => API_CONFIG.REALTIME_CPS_DATA.DATA_TYPES[type])
    .filter(Boolean)
    .join(",");

  return `${API_CONFIG.BASE_URL}${API_CONFIG.REALTIME_CPS_DATA.url}?rtKeyStr=${rtKeyStr}`;
}

/**
 * 获取默认的CPS查询日期范围（最近7天）
 * @returns {Object} 包含startDay和endDay的对象
 */
export function getDefaultCPSDateRange() {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - 6); // 最近7天

  const formatDate = (date) => {
    return date.toISOString().split("T")[0]; // YYYY-MM-DD格式
  };

  return {
    startDay: formatDate(startDate),
    endDay: formatDate(endDate),
  };
}

export const ColorOptions = {
  green: {
    color: "rgb(92, 239, 171)",
    colorWithWhite: "rgb(176, 255, 218)",
    colorTransparent: "rgba(92, 239, 171, 0.5)",
  },
  blue: {
    color: "rgb(68, 141, 245)",
    colorWithWhite: "rgb(156, 197, 255)",
    colorTransparent: "rgba(68, 141, 245, 0.5)",
  },
  yellow: {
    color: "rgb(236, 193, 121)",
    colorWithWhite: "rgb(250, 217, 161)",
    colorTransparent: "rgba(236, 193, 121, 0.5)",
  },
};

/**
 * 颜色主题配置
 */
export const CHART_COLORS = {
  primary: "#1a3a66",
  text: "#c0c0c0",
  label: "#6c7e9a",
  textSecondary: "#6c7e9a",
  textTitle: "#d1d7db",
  background: "rgba(10, 26, 51, 0.9)",
  backgroundLight: "rgba(10, 26, 51, 0.7)",
  border: "#1a3a66",
};

/**
 * 基础网格配置
 */
export const BASE_GRID_CONFIG = {
  left: "0%",
  right: "0%",
  bottom: "0%",
  containLabel: true,
};

/**
 * 基础提示框配置
 */
export const BASE_TOOLTIP_CONFIG = {
  trigger: "axis",
  backgroundColor: CHART_COLORS.background,
  borderColor: CHART_COLORS.border,
  textStyle: {
    color: "#fff",
  },
};

/**
 * 基础图例配置
 */
export const BASE_LEGEND_CONFIG = {
  textStyle: {
    color: CHART_COLORS.text,
  },
  top: "0%",
  right: "0%",
  itemHeight: 8,
  itemStyle: {
    borderWidth: 4,
  },
};

/**
 * 基础X轴配置
 */
export const BASE_XAXIS_CONFIG = {
  type: "category",
  axisLine: {
    lineStyle: {
      color: CHART_COLORS.primary,
    },
  },
  axisLabel: {
    color: CHART_COLORS.label,
  },
  splitLine: {
    show: false,
  },
};

/**
 * 基础Y轴配置
 */
export const BASE_YAXIS_CONFIG = {
  type: "value",
  nameTextStyle: {
    color: CHART_COLORS.textTitle,
  },
  axisLine: {
    lineStyle: {
      color: CHART_COLORS.primary,
    },
  },
  axisLabel: {
    color: CHART_COLORS.label,
  },
  splitLine: {
    lineStyle: {
      color: CHART_COLORS.primary,
      type: "dashed",
    },
  },
};

/**
 * 基础线条样式配置
 */
export const BASE_LINE_STYLE = {
  width: 2,
};

/**
 * 基础标记点配置
 */
export const BASE_MARK_POINT_CONFIG = {
  symbolSize: 6,
  symbol: "circle",
  itemStyle: {
    borderWidth: 4,
  },
  label: {
    show: true,
    position: "top",
    formatter: "{c}",
  },
};

/**
 * 获取网格配置
 * @param {Object} customConfig 自定义配置
 * @returns {Object} 合并后的网格配置
 */
export function getGridConfig(customConfig = {}) {
  return {
    ...BASE_GRID_CONFIG,
    ...customConfig,
  };
}

/**
 * 获取提示框配置
 * @param {Object} customConfig 自定义配置
 * @returns {Object} 合并后的提示框配置
 */
export function getTooltipConfig(customConfig = {}) {
  const defaultFormatter = (params) => {
    let result = `${params[0].axisValue}<br/>`;
    params.forEach((param) => {
      result += `${param.marker} ${param.seriesName}: ${param.value}<br/>`;
    });
    return result;
  };

  return {
    ...BASE_TOOLTIP_CONFIG,
    formatter: defaultFormatter,
    ...customConfig,
  };
}

/**
 * 获取图例配置
 * @param {Array} legendData 图例数据
 * @param {Object} customConfig 自定义配置
 * @returns {Object} 合并后的图例配置
 */
export function getLegendConfig(legendData, customConfig = {}) {
  return {
    ...BASE_LEGEND_CONFIG,
    data: legendData,
    ...customConfig,
  };
}

/**
 * 获取X轴配置
 * @param {Array} xAxisData X轴数据
 * @param {Object} customConfig 自定义配置
 * @returns {Object} 合并后的X轴配置
 */
export function getXAxisConfig(xAxisData, customConfig = {}) {
  return {
    ...BASE_XAXIS_CONFIG,
    data: xAxisData,
    ...customConfig,
  };
}

/**
 * 获取Y轴配置
 * @param {string} unit 单位
 * @param {Object} yAxisOptions Y轴选项 (min, max, interval, showMinorTick等)
 * @param {Object} customConfig 自定义配置
 * @returns {Object} 合并后的Y轴配置
 */
export function getYAxisConfig(unit, yAxisOptions = {}, customConfig = {}) {
  const config = {
    ...BASE_YAXIS_CONFIG,
    name: unit,
    min: yAxisOptions.min || null,
    max: yAxisOptions.max || null,
    interval: yAxisOptions.interval || null,
    ...customConfig,
  };

  // 如果需要显示次要刻度线
  if (yAxisOptions.showMinorTick) {
    config.minorSplitLine = {
      show: true,
      lineStyle: {
        color: CHART_COLORS.primary,
        opacity: 0.3,
      },
    };
  }

  return config;
}

/**
 * 获取标记点配置
 * @param {Object} currentValue 当前值信息 {value, position}
 * @param {string} color 颜色
 * @param {Object} customConfig 自定义配置
 * @returns {Object} 标记点配置
 */
export function getMarkPointConfig(
  currentValue,
  markPoint = {},
  customConfig = {}
) {
  if (!currentValue) return null;

  return {
    data: [
      {
        value: currentValue.value,
        xAxis: currentValue.position,
        yAxis: currentValue.value,
        itemStyle: {
          color: ColorOptions[markPoint.colorType].colorWithWhite,
          borderColor: ColorOptions[markPoint.colorType].colorTransparent,
        },
      },
    ],
    ...BASE_MARK_POINT_CONFIG,
    label: {
      ...BASE_MARK_POINT_CONFIG.label,
      color: ColorOptions[markPoint.colorType].color,
    },
    ...customConfig,
  };
}

/**
 * 创建基础线条系列配置
 * @param {Object} seriesItem 系列数据项
 * @param {Array} data 数据数组
 * @param {boolean} isDashed 是否为虚线
 * @param {Object} markPoint 标记点配置
 * @returns {Object} 系列配置
 */
export function createLineSeriesConfig(
  seriesItem,
  data,
  isDashed = false,
  markPoint = null
) {
  const { color } = ColorOptions[seriesItem.colorType];
  const config = {
    name: seriesItem.name,
    type: "line",
    data: data,
    smooth: true,
    symbol: "circle", //设置以后，legend会保持一致
    symbolSize: seriesItem.symbolSize || 6,
    showSymbol: seriesItem.showSymbol,
    itemStyle: {
      color: ColorOptions[seriesItem.colorType].colorWithWhite,
      borderWidth: 4,
      borderColor: ColorOptions[seriesItem.colorType].colorTransparent,
    },
    lineStyle: {
      ...BASE_LINE_STYLE,
      color,
    },
    row: seriesItem,
    colorType: seriesItem.colorType,
  };

  if (isDashed) {
    config.lineStyle.type = "dashed";
  }

  if (markPoint) {
    config.markPoint = markPoint;
  }

  return config;
}

/**
 * 处理实线虚线分割的系列数据
 * @param {Array} series 原始系列数据
 * @param {number} dashRatio 虚线开始位置比例 (默认0.6)
 * @param {Array} markers 标记线数据 (可选)
 * @returns {Array} 处理后的系列数据
 */
export function processDashedSeries(series, dashRatio = 0.6, markers = null) {
  const seriesData = series
    .map((item) => {
      const dataLength = item.data.length;
      const dashStartIndex = Math.floor(dataLength * dashRatio);

      // 分割数据为实线部分和虚线部分
      const solidData = item.data
        .slice(0, dashStartIndex)
        .map((value, index) => [index, value]);
      const dashData = item.data
        .slice(dashStartIndex - 1)
        .map((value, index) => [index + dashStartIndex - 1, value]);

      // 创建实线系列
      const solidSeries = createLineSeriesConfig(item, solidData, false);

      // 创建虚线系列
      const dashSeries = createLineSeriesConfig(item, dashData, true);

      // 处理标记点
      if (item.currentValue) {
        const markPointConfig = getMarkPointConfig(item.currentValue, item);
        if (item.currentValue.position < dashStartIndex) {
          solidSeries.markPoint = markPointConfig;
        } else {
          dashSeries.markPoint = markPointConfig;
        }
      }

      return [solidSeries, dashSeries];
    })
    .flat();

  // 处理标记线
  // if (markers && markers.length > 0) {
  //   addMarkersToSeries(seriesData, markers);
  // }

  return seriesData;
}

/**
 * 为系列数据添加标记线
 * @param {Array} seriesData 系列数据
 * @param {Array} markers 标记线数据
 */
export function addMarkersToSeries(seriesData, markers = []) {
  markers.forEach((marker) => {
    // 找到对应的系列
    const targetSeries = seriesData.find(
      (s) => s.colorType === marker.colorType
    );
    if (targetSeries) {
      if (!targetSeries.markLine) {
        targetSeries.markLine = {
          data: [],
          lineStyle: {
            color: ColorOptions[marker.colorType].color,
            type: "dashed",
          },
          label: {
            formatter: marker.text,
            position: "insideMiddleTop",
            backgroundColor: CHART_COLORS.backgroundLight,
            padding: [2, 6],
            borderRadius: 3,
          },
        };
      }

      targetSeries.markLine.data.push([
        {
          xAxis: marker.position.x,
          yAxis: marker.position.y,
          value: marker.value,
        },
        {
          xAxis: marker.position.x,
          yAxis: marker.value,
        },
      ]);
    }
  });
}

/**
 * 处理简单线条系列数据（不分割实线虚线）
 * @param {Array} series 原始系列数据
 * @returns {Array} 处理后的系列数据
 */
export function processSimpleSeries(series) {
  return series.map((item) => {
    return createLineSeriesConfig(item, item.data, false);
  });
}

/**
 * 创建完整的图表配置
 * @param {Object} options 配置选项
 * @returns {Object} 完整的ECharts配置
 */
export function createChartOption(options) {
  const {
    xAxis,
    series,
    unit,
    yAxis,
    gridConfig = {},
    tooltipConfig = {},
    legendConfig = {},
    xAxisConfig = {},
    yAxisConfig = {},
    graphicConfig = [],
  } = options;

  // 构建图例数据
  const legendData = series.map((item) => {
    return {
      name: item.name,
      itemStyle: {
        color: ColorOptions[item.colorType].colorWithWhite,
        borderColor: ColorOptions[item.colorType].colorTransparent,
      },
    };
  });

  return {
    grid: getGridConfig(gridConfig),
    tooltip: getTooltipConfig(tooltipConfig),
    legend: getLegendConfig(legendData, legendConfig),
    xAxis: getXAxisConfig(xAxis, xAxisConfig),
    yAxis: getYAxisConfig(unit, yAxis, yAxisConfig),
    graphic: graphicConfig,
    series,
  };
}

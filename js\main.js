/**
 * 主程序入口
 */
// 引入第三方库
import * as echarts from "echarts";
import * as d3 from "d3";

// 引入组件
import CPSChart from "./components/CPSChart.js";
import InfoDisplay from "./components/InfoDisplay.js";
import SectionTable from "./components/SectionTable.js";
import cpsData from "./data/cps.json";

// 将库挂载到全局对象，保持与原有代码的兼容性
window.echarts = echarts;
window.d3 = d3;

// VITE_CHART_BANLANCE VITE_CHART_WIND VITE_CHART_SOLAR
// 将三个iframe的地址分别设置

// 页面加载完成后初始化所有图表
document.addEventListener("DOMContentLoaded", () => {
  // 存储所有组件实例
  const components = {};

  // 初始化平衡曲线图表
  // components.balanceChart = new BalanceChart("balance-chart", data.balanceData);

  // 初始化风电消纳曲线图表
  // components.windChart = new WindChart("wind-chart", data.windData);

  // 初始化光伏消纳曲线图表
  // components.solarChart = new SolarChart("solar-chart", data.solarData);

  // 初始化断面监视表格
  components.sectionTable = new SectionTable("section-table");

  // 初始化CPS曲线图表（不传入静态数据，让组件自动从API获取并启动定时刷新）
  components.cpsChart = new CPSChart(
    "cps-chart",
    import.meta.env.DEV ? cpsData.data : null
  );

  // 初始化信息显示（不传入静态数据，让组件自动从API获取）
  components.infoDisplay = new InfoDisplay("info-container");

  // 将组件实例挂载到全局对象，便于调试和控制
  window.balanceMonitorComponents = components;

  console.log("全网平衡监视系统初始化完成", components);
});

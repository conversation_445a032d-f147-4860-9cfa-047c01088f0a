/**
 * 主程序入口
 */
// 引入第三方库
import * as echarts from "echarts";
import * as d3 from "d3";

// 引入组件
import CPSChart from "./components/CPSChart.js";
import InfoDisplay from "./components/InfoDisplay.js";
import SectionTable from "./components/SectionTable.js";

// 将库挂载到全局对象，保持与原有代码的兼容性
window.echarts = echarts;
window.d3 = d3;

/**
 * 设置三个iframe的地址
 * 从环境变量中获取URL并设置到对应的iframe
 */
function setupIframeUrls() {
  // 获取环境变量中的URL
  // const balanceUrl = import.meta.env.VITE_CHART_BALANCE;
  // const windUrl = import.meta.env.VITE_CHART_WIND;
  // const solarUrl = import.meta.env.VITE_CHART_SOLAR;

  const chartUrl = import.meta.env.VITE_CHART_URL;

  // 获取iframe元素
  // const balanceIframe = document.querySelector(".balance-chart-layout iframe");
  // const windIframe = document.querySelector(".wind-chart-layout iframe");
  // const solarIframe = document.querySelector(".solar-chat-layout iframe");
  const mainIframe = document.querySelector(".top-layout iframe");

  // 设置iframe的src属性
  // if (balanceIframe && balanceUrl) {
  //   balanceIframe.src = balanceUrl;
  //   console.log("平衡图表iframe地址已设置:", balanceUrl);
  // } else {
  //   console.warn("平衡图表iframe或URL未找到", {
  //     iframe: !!balanceIframe,
  //     url: balanceUrl,
  //   });
  // }

  // if (windIframe && windUrl) {
  //   windIframe.src = windUrl;
  //   console.log("风电图表iframe地址已设置:", windUrl);
  // } else {
  //   console.warn("风电图表iframe或URL未找到", {
  //     iframe: !!windIframe,
  //     url: windUrl,
  //   });
  // }

  // if (solarIframe && solarUrl) {
  //   solarIframe.src = solarUrl;
  //   console.log("光伏图表iframe地址已设置:", solarUrl);
  // } else {
  //   console.warn("光伏图表iframe或URL未找到", {
  //     iframe: !!solarIframe,
  //     url: solarUrl,
  //   });
  // }

  if (mainIframe && chartUrl) {
    mainIframe.src = chartUrl;
    console.log("主图表iframe地址已设置:", chartUrl);
  } else {
    console.warn("主图表iframe或URL未找到", {
      iframe: !!mainIframe,
      url: chartUrl,
    });
  }
}

// 页面加载完成后初始化所有图表
document.addEventListener("DOMContentLoaded", () => {
  // 存储所有组件实例
  const components = {};

  // 设置三个iframe的地址
  setupIframeUrls();

  // 初始化平衡曲线图表
  // components.balanceChart = new BalanceChart("balance-chart", data.balanceData);

  // 初始化风电消纳曲线图表
  // components.windChart = new WindChart("wind-chart", data.windData);

  // 初始化光伏消纳曲线图表
  // components.solarChart = new SolarChart("solar-chart", data.solarData);

  // 初始化断面监视表格
  components.sectionTable = new SectionTable("section-table");

  // 初始化CPS曲线图表（不传入静态数据，让组件自动从API获取并启动定时刷新）
  components.cpsChart = new CPSChart("cps-chart");

  // 初始化信息显示（不传入静态数据，让组件自动从API获取）
  components.infoDisplay = new InfoDisplay("info-container");

  // 将组件实例挂载到全局对象，便于调试和控制
  window.balanceMonitorComponents = components;

  console.log("全网平衡监视系统初始化完成", components);
});
